{"level":"info","message":"Connected to MongoDB","service":"alidata-backend","timestamp":"2025-07-31T23:27:18.317Z"}
{"context":{"environment":"development","nodeVersion":"v20.17.0","port":"5000"},"level":"info","message":"Server running on port 5000","service":"alidata-backend","timestamp":"2025-07-31T23:27:18.333Z"}
{"context":{"plansCount":20},"level":"info","message":"Data plans retrieved","service":"alidata-backend","timestamp":"2025-07-31T23:27:40.513Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"alidata-backend","timestamp":"2025-07-31T23:29:42.897Z"}
