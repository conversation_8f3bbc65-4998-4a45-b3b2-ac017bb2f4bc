{"name": "alidata-backend", "version": "1.0.0", "description": "Express.js backend for Alidata VTU mobile app", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "^1.10.1", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.1.0", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}